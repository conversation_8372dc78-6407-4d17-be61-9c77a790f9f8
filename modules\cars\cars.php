<?php
/**
 * Module Name: DSTweaks Cars Module
 * Description: A module for managing car-related functionalities in DSTweaks plugin.
 * Version: 4.0.1
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_Cars_Module {
    // Unified custom fields array with translatable labels
    private $car_fields = [
        // Integer fields
        [ 'slug' => 'engine_power', 'label' => 'Engine Power', 'type' => 'number', 'label_trans' => 'Engine Power' ],
        [ 'slug' => 'torque', 'label' => 'Torque', 'type' => 'number', 'label_trans' => 'Torque' ],
        [ 'slug' => 'price', 'label' => 'Price', 'type' => 'number', 'label_trans' => 'Price' ],
        [ 'slug' => 'driving_range', 'label' => 'Driving Range (e-cars only)', 'type' => 'number', 'label_trans' => 'Driving Range (e-cars only)' ],
        // Float fields
        [ 'slug' => 'battery_capacity', 'label' => 'Battery Capacity (e-cars only)', 'type' => 'float', 'label_trans' => 'Battery Capacity (e-cars only)' ],
        // Text fields
        [ 'slug' => 'power_transmission', 'label' => 'Power Transmission', 'type' => 'text', 'label_trans' => 'Power Transmission' ],
        [ 'slug' => 'gearbox', 'label' => 'Gearbox', 'type' => 'text', 'label_trans' => 'Gearbox' ],
        [ 'slug' => 'slide1_title', 'label' => 'Slide 1 Title', 'type' => 'text', 'label_trans' => 'Slide 1 Title' ],
        [ 'slug' => 'slide2_title', 'label' => 'Slide 2 Title', 'type' => 'text', 'label_trans' => 'Slide 2 Title' ],
        [ 'slug' => 'slide3_title', 'label' => 'Slide 3 Title', 'type' => 'text', 'label_trans' => 'Slide 3 Title' ],
        // Textarea fields
        [ 'slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'label_trans' => 'Description' ],
        [ 'slug' => 'slide1_text', 'label' => 'Slide 1 Text', 'type' => 'textarea', 'label_trans' => 'Slide 1 Text' ],
        [ 'slug' => 'slide2_text', 'label' => 'Slide 2 Text', 'type' => 'textarea', 'label_trans' => 'Slide 2 Text' ],
        [ 'slug' => 'slide3_text', 'label' => 'Slide 3 Text', 'type' => 'textarea', 'label_trans' => 'Slide 3 Text' ],
        [ 'slug' => 'driving_force', 'label' => 'Driving Force', 'type' => 'textarea', 'label_trans' => 'Driving Force' ],
        [ 'slug' => 'driver_assist', 'label' => 'Driver Assistance', 'type' => 'textarea', 'label_trans' => 'Driver Assistance' ],
        [ 'slug' => 'multimedia', 'label' => 'Multimedia', 'type' => 'textarea', 'label_trans' => 'Multimedia' ],
        [ 'slug' => 'steering', 'label' => 'Steering', 'type' => 'textarea', 'label_trans' => 'Steering' ],
        [ 'slug' => 'lighting', 'label' => 'Lighting System', 'type' => 'textarea', 'label_trans' => 'Lighting System' ],
        [ 'slug' => 'comfort', 'label' => 'Comfort Features', 'type' => 'textarea', 'label_trans' => 'Comfort Features' ],
        [ 'slug' => 'brake_system', 'label' => 'Brake System', 'type' => 'textarea', 'label_trans' => 'Brake System' ],
        [ 'slug' => 'safety', 'label' => 'Safety', 'type' => 'textarea', 'label_trans' => 'Safety' ],
        [ 'slug' => 'dimensions', 'label' => 'Weight and Dimensions', 'type' => 'textarea', 'label_trans' => 'Weight and Dimensions' ],
        // Image fields
        [ 'slug' => 'description_image', 'label' => 'Description Image', 'type' => 'image', 'label_trans' => 'Description Image' ],
        [ 'slug' => 'slide1_image', 'label' => 'Slide 1 Image', 'type' => 'image', 'label_trans' => 'Slide 1 Image' ],
        [ 'slug' => 'slide2_image', 'label' => 'Slide 2 Image', 'type' => 'image', 'label_trans' => 'Slide 2 Image' ],
        [ 'slug' => 'slide3_image', 'label' => 'Slide 3 Image', 'type' => 'image', 'label_trans' => 'Slide 3 Image' ],
        [ 'slug' => 'car_icon', 'label' => 'Car Icon', 'type' => 'image', 'label_trans' => 'Car Icon' ],
        // Gallery field
        [ 'slug' => 'car_gallery', 'label' => 'Image Gallery', 'type' => 'gallery', 'label_trans' => 'Image Gallery' ],
        // File field
        [ 'slug' => 'catalogue', 'label' => 'Catalogue', 'type' => 'file', 'label_trans' => 'Catalogue' ],
        // Select fields
        [ 'slug' => 'car_type', 'label' => 'Car Type', 'type' => 'select', 'label_trans' => 'Car Type', 'options' => [
            'sedan' => 'Sedan',
            'suv' => 'SUV',
            'crossover' => 'Crossover'
        ]],
        [ 'slug' => 'fuel_type', 'label' => 'Fuel Type', 'type' => 'select', 'label_trans' => 'Fuel Type', 'options' => [
            'gasoline' => 'Gasoline',
            'hybrid' => 'Hybrid (Electric & Gasoline)',
            'electric' => 'Electric'
        ]],
    ];

    public function __construct() {
        add_action('init', array($this, 'register_car_post_type'));
        add_action('init', array($this, 'register_car_meta'));
        add_action('do_meta_boxes', array($this, 'remove_default_meta_boxes'));
        add_action('admin_init', array($this, 'remove_project_editor'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('add_meta_boxes', array($this, 'add_car_meta_boxes'), 10);
        add_action('save_post', array($this, 'save_car_meta'));
    }

    /**
     * Register custom post type 'car'
     *
     * @return void
     */
    public function register_car_post_type() {
        $singular_name = __('Car', 'dstweaks');
        $plural_name = __('Cars', 'dstweaks');
        $slug = 'cars';
        $menu_icon = 'dashicons-car';

        register_post_type('project', [
            'labels' => [
                'name' => $plural_name,
                'singular_name' => $singular_name,
                /* translators: %s: Car singular name */
                'add_new_item' => sprintf(__('Add New %s', 'dstweaks'), $singular_name),
                /* translators: %s: Car singular name */
                'edit_item' => sprintf(__('Edit %s', 'dstweaks'), $singular_name),
                /* translators: %s: Car singular name */
                'new_item' => sprintf(__('New %s', 'dstweaks'), $singular_name),
                /* translators: %s: Car plural name */
                'all_items' => sprintf(__('All %s', 'dstweaks'), $plural_name),
                /* translators: %s: Car singular name */
                'view_item' => sprintf(__('View %s', 'dstweaks'), $singular_name),
                /* translators: %s: Car plural name */
                'search_items' => sprintf(__('Search %s', 'dstweaks'), $plural_name),
            ],
            'menu_icon'   => $menu_icon,
            'has_archive' => true,
            'hierarchical' => true,
            'public' => true,
            'rewrite' => [
                'slug' => $slug,
            ],
            'supports' => [
                'title',
                'thumbnail',
                'excerpt',
                'custom-fields',
                'revisions',
            ],
        ]);

        $category_singular_name = __('Car Brand', 'dstweaks');
        $category_plural_name   = __('Car Brands', 'dstweaks');
        $category_slug          = 'car_brand';

        register_taxonomy('project_category', array('project'), [
            'hierarchical' => true,
            'labels' => [
                'name' => $category_plural_name,
                'singular_name' => $category_singular_name,
                /* translators: %s: Car brand plural name */
                'search_items' => sprintf(__('Search %s', 'dstweaks'), $category_plural_name),
                /* translators: %s: Car brand plural name */
                'all_items' => sprintf(__('All %s', 'dstweaks'), $category_plural_name),
                /* translators: %s: Car brand singular name */
                'parent_item' => sprintf(__('Parent %s', 'dstweaks'), $category_singular_name),
                /* translators: %s: Car brand singular name */
                'parent_item_colon' => sprintf(__('Parent %s:', 'dstweaks'), $category_singular_name),
                /* translators: %s: Car brand singular name */
                'edit_item' => sprintf(__('Edit %s', 'dstweaks'), $category_singular_name),
                /* translators: %s: Car brand singular name */
                'update_item' => sprintf(__('Update %s', 'dstweaks'), $category_singular_name),
                /* translators: %s: Car brand singular name */
                'add_new_item' => sprintf(__('Add New %s', 'dstweaks'), $category_singular_name),
                /* translators: %s: Car brand singular name */
                'new_item_name' => sprintf(__('New %s Name', 'dstweaks'), $category_singular_name),
                'menu_name' => $category_plural_name,
                /* translators: %s: Car brand plural name */
                'not_found' => sprintf(__('You currently don\'t have any %s.', 'dstweaks'), $category_plural_name),
            ],
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'show_in_rest' => true,
            'rewrite' => [
                'slug' => $category_slug,
                'with_front' => true,
            ],
        ]);

        $tag_singular_name = __('Car Tag', 'dstweaks');
        $tag_plural_name   = __('Car Tags', 'dstweaks');
        $tag_slug          = 'car_tag';

        register_taxonomy('project_tag', array('project'), [
            'hierarchical' => false,
            'labels' => [
                'name' => $tag_plural_name,
                'singular_name' => $tag_singular_name,
                /* translators: %s: Car tag plural name */
                'search_items' => sprintf(__('Search %s', 'dstweaks'), $tag_plural_name),
                /* translators: %s: Car tag plural name */
                'all_items' => sprintf(__('All %s', 'dstweaks'), $tag_plural_name),
                /* translators: %s: Car tag singular name */
                'parent_item' => sprintf(__('Parent %s', 'dstweaks'), $tag_singular_name),
                /* translators: %s: Car tag singular name */
                'parent_item_colon' => sprintf(__('Parent %s:', 'dstweaks'), $tag_singular_name),
                /* translators: %s: Car tag singular name */
                'edit_item' => sprintf(__('Edit %s', 'dstweaks'), $tag_singular_name),
                /* translators: %s: Car tag singular name */
                'update_item' => sprintf(__('Update %s', 'dstweaks'), $tag_singular_name),
                /* translators: %s: Car tag singular name */
                'add_new_item' => sprintf(__('Add New %s', 'dstweaks'), $tag_singular_name),
                /* translators: %s: Car tag singular name */
                'new_item_name' => sprintf(__('New %s Name', 'dstweaks'), $tag_singular_name),
                'menu_name' => $tag_plural_name,
                /* translators: %s: Car tag plural name */
                'not_found' => sprintf(__('You currently don\'t have any %s.', 'dstweaks'), $tag_plural_name),
            ],
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'show_in_rest' => true,
            'rewrite' => [
                'slug' => $tag_slug,
                'with_front' => true,
            ],
        ]);
    }

    /**
     * Register custom meta fields for cars
     *
     * @return void
     */
    public function register_car_meta() {
        foreach ($this->car_fields as $field) {
            $args = [
                'single' => true,
                'show_in_rest' => true,
            ];
            switch ($field['type']) {
                case 'number':
                    $args['type'] = 'number';
                    break;
                case 'float':
                    $args['type'] = 'string';
                    $args['sanitize_callback'] = array($this, 'sanitize_float_field');
                    break;
                case 'text':
                    $args['type'] = 'string';
                    break;
                case 'textarea':
                    $args['type'] = 'string';
                    $args['sanitize_callback'] = 'wp_kses_post';
                    break;
                case 'image':
                case 'file':
                    $args['type'] = 'number';
                    break;
                case 'gallery':
                case 'select':
                    $args['type'] = 'string';
                    $args['sanitize_callback'] = 'sanitize_text_field';
                    break;
            }
            register_meta('post', $field['slug'], $args);
        }
    }

    /**
     * Remove unwanted default meta boxes
     *
     * @return void
     */
    public function remove_default_meta_boxes() {
        // Remove the author meta box
        remove_meta_box('authordiv', 'project', 'normal');
        
        // Remove the slug meta box
        remove_meta_box('slugdiv', 'project', 'normal');
        
        // Remove the custom fields meta box
        remove_meta_box('postcustom', 'project', 'normal');
        
        // Remove the revisions meta box
        remove_meta_box('revisionsdiv', 'project', 'normal');
    }

    /**
     * Ensure the post content editor is removed for 'project' post type
     *
     * @return void
     */
    public function remove_project_editor() {
        remove_post_type_support('project', 'editor');
    }

    /**
     * Enqueue admin scripts for media and editor
     *
     * @param string $hook Current admin page hook
     * @return void
     */
    public function enqueue_admin_scripts($hook) {
        if ('post.php' != $hook && 'post-new.php' != $hook) {
            return;
        }
        wp_enqueue_media();
        wp_enqueue_editor();
    }

    /**
     * Add meta boxes for car details
     *
     * @return void
     */
    public function add_car_meta_boxes() {
        // General Details group (1st)
        add_meta_box(
            'general_details_meta_box',
            __('General Details', 'dstweaks'),
            array($this, 'render_general_details_meta_box'),
            'project',
            'normal',
            'default'
        );

        // Highlighted Features group (2nd)
        add_meta_box(
            'highlighted_features_meta_box',
            __('Highlighted Features', 'dstweaks'),
            array($this, 'render_highlighted_features_meta_box'),
            'project',
            'normal',
            'default'
        );

        // Description group (3rd)
        add_meta_box(
            'description_meta_box',
            __('Description', 'dstweaks'),
            array($this, 'render_description_meta_box'),
            'project',
            'normal',
            'default'
        );

        // Slider group (4th)
        add_meta_box(
            'slider_meta_box',
            __('Slider', 'dstweaks'),
            array($this, 'render_slider_meta_box'),
            'project',
            'normal',
            'default'
        );

        // Features group (5th)
        add_meta_box(
            'features_meta_box',
            __('Features', 'dstweaks'),
            array($this, 'render_features_meta_box'),
            'project',
            'normal',
            'default'
        );

        // Image Gallery group (6th)
        add_meta_box(
            'image_gallery_meta_box',
            __('Image Gallery', 'dstweaks'),
            array($this, 'render_image_gallery_meta_box'),
            'project',
            'normal',
            'default'
        );
    }
    /**
     * Highlighted Features Section
     *
     * @param WP_Post $post Current post object
     * @return void
     */
    public function render_highlighted_features_meta_box($post) {
        wp_nonce_field('car_meta_box', 'car_meta_box_nonce');
        ?>
        <div class="car-meta-box">
            <?php
            foreach ($this->car_fields as $field) {
                if (in_array($field['slug'], ['engine_power', 'power_transmission', 'gearbox', 'torque', 'battery_capacity', 'driving_range'])) {
                    $this->render_field($field, $post->ID);
                }
            }
            ?>
        </div>
        <?php
    }
    // Description Section
    public function render_description_meta_box($post) {
        ?>
        <div class="car-meta-box">
            <div class="editor-wrapper">
                <p>
                    <label for="description"><strong><?php echo esc_html(__('Description', 'dstweaks')); ?></strong></label>
                </p>
                <?php 
                wp_editor(
                    get_post_meta($post->ID, 'description', true),
                    'description',
                    array(
                        'media_buttons' => true,
                        'textarea_rows' => 5,
                        'teeny' => true,
                        'quicktags' => array('buttons' => 'strong,em,ul,ol,li,link')
                    )
                );
                ?>
            </div>
            <div class="image-field">
                <p>
                    <label><strong><?php echo esc_html(__('Description Image', 'dstweaks')); ?></strong></label>
                    <div class="image-preview-wrapper">
                        <?php
                        $image_id = get_post_meta($post->ID, 'description_image', true);
                        $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : '';
                        ?>
                        <div class="image-preview" id="description_image-preview">
                            <?php if ($image_url): ?>
                                <img src="<?php echo esc_url($image_url); ?>" />
                            <?php endif; ?>
                        </div>
                        <input type="hidden" name="description_image" id="description_image" value="<?php echo esc_attr($image_id); ?>">
                        <button type="button" class="upload-image-button button" data-field="description_image">
                            <?php echo esc_html(__('Select Image', 'dstweaks')); ?>
                        </button>
                        <button type="button" class="remove-image-button button" data-field="description_image" <?php echo !$image_id ? 'style=\"display:none;\"' : ''; ?>>
                            <?php echo esc_html(__('Remove Image', 'dstweaks')); ?>
                        </button>
                    </div>
                </p>
            </div>
        </div>
        <script>
        jQuery(document).ready(function($) {
            $('.upload-file-button').click(function(e) {
                e.preventDefault();
                var button = $(this);
                var field = button.data('field');
                var frame = wp.media({
                    title: '<?php echo esc_js(__('Select File', 'dstweaks')); ?>',
                    multiple: false,
                    library: { type: 'application/pdf' }
                });
                frame.on('select', function() {
                    var attachment = frame.state().get('selection').first().toJSON();
                    $('#' + field + '-preview').html('<span>' + attachment.title + '.' + attachment.subtype + '</span>');
                    $('#' + field).val(attachment.id);
                    button.siblings('.remove-file-button').show();
                });
                frame.open();
            });
            $('.remove-file-button').click(function() {
                var field = $(this).data('field');
                $('#' + field + '-preview').empty();
                $('#' + field).val('');
                $(this).hide();
            });
        });
        </script>
        <?php
    }
    // Slider Section
    public function render_slider_meta_box($post) {
        ?>
        <div class="car-meta-box">
            <?php for ($i = 1; $i <= 3; $i++): ?>
                <div class="slider-section">
                    <h4><?php printf(esc_html__('Slide %d', 'dstweaks'), $i); ?></h4>
                    <div class="editor-wrapper">
                        <p>
                            <label for="slide<?php echo $i; ?>_title">
                                <strong><?php printf(esc_html__('Slide %d Title:', 'dstweaks'), $i); ?></strong>
                            </label>
                        </p>
                        <input type="text" id="slide<?php echo $i; ?>_title" name="slide<?php echo $i; ?>_title" value="<?php echo esc_attr(get_post_meta($post->ID, "slide{$i}_title", true)); ?>" style="width:100%;max-width:600px;">
                    </div>
                    <div class="editor-wrapper">
                        <p>
                            <label for="slide<?php echo $i; ?>_text">
                                <strong><?php printf(esc_html__('Slide %d Text:', 'dstweaks'), $i); ?></strong>
                            </label>
                        </p>
                        <?php 
                        wp_editor(
                            get_post_meta($post->ID, "slide{$i}_text", true),
                            "slide{$i}_text",
                            array(
                                'media_buttons' => true,
                                'textarea_rows' => 5,
                                'teeny' => true,
                                'quicktags' => array('buttons' => 'strong,em,ul,ol,li,link')
                            )
                        );
                        ?>
                    </div>
                    <div class="image-field">
                        <p>
                            <label><strong><?php printf(esc_html__('Slide %d Image:', 'dstweaks'), $i); ?></strong></label>
                            <div class="image-preview-wrapper">
                                <?php
                                $image_id = get_post_meta($post->ID, "slide{$i}_image", true);
                                $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : '';
                                ?>
                                <div class="image-preview" id="slide<?php echo $i; ?>_image-preview">
                                    <?php if ($image_url): ?>
                                        <img src="<?php echo esc_url($image_url); ?>" />
                                    <?php endif; ?>
                                </div>
                                <input type="hidden" name="slide<?php echo $i; ?>_image" id="slide<?php echo $i; ?>_image" value="<?php echo esc_attr($image_id); ?>">
                                <button type="button" class="upload-image-button button" data-field="slide<?php echo $i; ?>_image">
                                    <?php esc_html_e('Select Image', 'dstweaks'); ?>
                                </button>
                                <button type="button" class="remove-image-button button" data-field="slide<?php echo $i; ?>_image" <?php echo !$image_id ? 'style=\"display:none;\"' : ''; ?>>
                                    <?php esc_html_e('Remove Image', 'dstweaks'); ?>
                                </button>
                            </div>
                        </p>
                    </div>
                </div>
            <?php endfor; ?>
        </div>
        <script>
        jQuery(document).ready(function($) {
            // Only single image upload logic for slider images
            $('.upload-image-button').click(function(e) {
                e.preventDefault();
                var button = $(this);
                var field = button.data('field');
                var frame = wp.media({
                    title: '<?php echo esc_js(__('Select Image', 'dstweaks')); ?>',
                    multiple: false
                });
                frame.on('select', function() {
                    var attachment = frame.state().get('selection').first().toJSON();
                    $('#' + field + '-preview').html('<img src="' + attachment.sizes.thumbnail.url + '" />');
                    $('#' + field).val(attachment.id);
                    button.siblings('.remove-image-button').show();
                });
                frame.open();
            });
            $('.remove-image-button').click(function() {
                var field = $(this).data('field');
                $('#' + field + '-preview').empty();
                $('#' + field).val('');
                $(this).hide();
            });
        });
        </script>
        <?php
    }
    // General Details Section
    public function render_general_details_meta_box($post) {
        $price = get_post_meta($post->ID, 'price', true);
        ?>
        <div class="car-meta-box">
            <!-- Car Icon -->
            <div class="image-field">
                <p>
                    <label><strong><?php echo esc_html(__('Car Icon', 'dstweaks')); ?></strong></label>
                    <div class="image-preview-wrapper">
                        <?php
                        $image_id = get_post_meta($post->ID, 'car_icon', true);
                        $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'thumbnail') : '';
                        ?>
                        <div class="image-preview" id="car_icon-preview">
                            <?php if ($image_url): ?>
                                <img src="<?php echo esc_url($image_url); ?>" />
                            <?php endif; ?>
                        </div>
                        <input type="hidden" name="car_icon" id="car_icon" value="<?php echo esc_attr($image_id); ?>">
                        <button type="button" class="upload-image-button button" data-field="car_icon">
                            <?php echo esc_html(__('Select Image', 'dstweaks')); ?>
                        </button>
                        <button type="button" class="remove-image-button button" data-field="car_icon" <?php echo !$image_id ? 'style=\"display:none;\"' : ''; ?>>
                            <?php echo esc_html(__('Remove Image', 'dstweaks')); ?>
                        </button>
                    </div>
                </p>
            </div>

            <!-- Price -->
            <p>
                <label for="price"><?php esc_html_e('Price:', 'dstweaks'); ?></label>
                <input type="text" id="price" name="price" value="<?php echo esc_attr($price); ?>" autocomplete="off">
            </p>

            <!-- Car Type and Fuel Type -->
            <?php
            foreach ($this->car_fields as $field) {
                if ($field['type'] === 'select' && in_array($field['slug'], ['car_type', 'fuel_type'])) {
                    $this->render_select_field($field, $post->ID);
                }
            }
            ?>

            <!-- Catalogue -->
            <div class="file-field">
                <p>
                    <label><strong><?php echo esc_html(__('Catalogue', 'dstweaks')); ?></strong></label>
                    <div class="file-preview-wrapper">
                        <?php
                        $file_id = get_post_meta($post->ID, 'catalogue', true);
                        $file_name = '';
                        if ($file_id) {
                            $file = get_post($file_id);
                            if ($file) $file_name = esc_html($file->post_title) . '.' . pathinfo($file->guid, PATHINFO_EXTENSION);
                        }
                        ?>
                        <div class="file-preview" id="catalogue-preview">
                            <?php if ($file_name): ?>
                                <span><?php echo $file_name; ?></span>
                            <?php endif; ?>
                        </div>
                        <input type="hidden" name="catalogue" id="catalogue" value="<?php echo esc_attr($file_id); ?>">
                        <button type="button" class="upload-file-button button" data-field="catalogue">
                            <?php echo esc_html(__('Select File', 'dstweaks')); ?>
                        </button>
                        <button type="button" class="remove-file-button button" data-field="catalogue" <?php echo !$file_id ? 'style=\"display:none;\"' : ''; ?>>
                            <?php echo esc_html(__('Remove File', 'dstweaks')); ?>
                        </button>
                    </div>
                </p>
            </div>
        </div>
        <script>
        jQuery(document).ready(function($) {
            var $price = $('#price');
            function formatNumber(n) {
                return n.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }
            function unformatNumber(n) {
                return n.replace(/,/g, '');
            }
            // Initial formatting if value exists
            if ($price.val()) {
                $price.val(formatNumber($price.val()));
            }
            $price.on('input', function() {
                var cursor = this.selectionStart;
                var oldValue = this.value;
                var raw = unformatNumber(this.value);
                var formatted = formatNumber(raw);

                // Calculate new cursor position
                var newCursor = cursor;

                // Count commas before cursor in old value
                var commasBefore = (oldValue.substring(0, cursor).match(/,/g) || []).length;

                // Count commas before cursor in new formatted value
                var rawBeforeCursor = unformatNumber(oldValue.substring(0, cursor));
                var formattedBeforeCursor = formatNumber(rawBeforeCursor);
                var newCommasBefore = (formattedBeforeCursor.match(/,/g) || []).length;

                // Adjust cursor position based on comma difference
                newCursor = cursor + (newCommasBefore - commasBefore);

                // Ensure cursor doesn't go beyond string length
                newCursor = Math.min(newCursor, formatted.length);

                this.value = formatted;
                this.setSelectionRange(newCursor, newCursor);
            });
            // On form submit, unformat so only the number is sent
            $('form#post').on('submit', function() {
                $price.val(unformatNumber($price.val()));
            });
        });
        </script>
        <?php
    }
    // Features Section
    public function render_features_meta_box($post) {
        ?>
        <div class="car-meta-box">
            <?php
            foreach ($this->car_fields as $field) {
                if (in_array($field['slug'], ['driving_force', 'driver_assist', 'multimedia', 'steering', 'lighting', 'comfort', 'brake_system', 'safety', 'dimensions'])) {
                    ?>
                    <div class="editor-wrapper">
                        <p>
                            <label for="<?php echo esc_attr($field['slug']); ?>"><strong><?php echo esc_html(__($field['label_trans'], 'dstweaks')); ?>:</strong></label>
                        </p>
                        <?php 
                        wp_editor(
                            get_post_meta($post->ID, $field['slug'], true),
                            $field['slug'],
                            array(
                                'media_buttons' => true,
                                'textarea_rows' => 5,
                                'teeny' => true,
                                'quicktags' => array('buttons' => 'strong,em,ul,ol,li,link')
                            )
                        );
                        ?>
                    </div>
                    <?php
                }
            }
            ?>
        </div>
        <?php
    }
    // Image Gallery Section
    public function render_image_gallery_meta_box($post) {
        ?>
        <div class="car-meta-box">
            <div class="gallery-field">
                <p>
                    <label><strong><?php esc_html_e('Image Gallery:', 'dstweaks'); ?></strong></label>
                    <div id="car-gallery-container">
                        <?php
                        $gallery = get_post_meta($post->ID, 'car_gallery', true);
                        $gallery_ids = [];
                        if ($gallery) {
                            $gallery_ids = array_filter(array_map('absint', explode(',', $gallery)));
                        }
                        if (!empty($gallery_ids)) {
                            foreach ($gallery_ids as $image_id) {
                                $image_url = wp_get_attachment_image_url($image_id, 'thumbnail');
                                if ($image_url) {
                                    echo '<div class="gallery-image" data-id="' . esc_attr($image_id) . '">';
                                    echo '<img src="' . esc_url($image_url) . '" />';
                                    echo '<button type="button" class="remove-image">×</button>';
                                    echo '</div>';
                                }
                            }
                        }
                        ?>
                    </div>
                    <input type="hidden" name="car_gallery" id="car_gallery" value="<?php echo esc_attr(implode(',', $gallery_ids)); ?>">
                    <button type="button" class="button" id="add-gallery-images">
                        <?php esc_html_e('Add Images', 'dstweaks'); ?>
                    </button>
                </p>
            </div>
        </div>

        <style>
            .car-meta-box label {
                display: inline-block;
                width: 120px;
                font-weight: bold;
            }
            .car-meta-box input[type="text"],
            .car-meta-box input[type="number"],
            .car-meta-box select {
                width: 200px;
            }
            .editor-wrapper {
                margin: 20px 0;
            }
            .image-preview-wrapper {
                margin: 10px 0;
            }
            .image-preview {
                max-width: 150px;
                margin: 10px 0;
            }
            .image-preview img {
                max-width: 100%;
                height: auto;
            }
            .gallery-field .gallery-image {
                display: inline-block;
                margin: 5px;
                position: relative;
            }
            .gallery-field .gallery-image img {
                max-width: 100px;
                height: auto;
            }
            .gallery-field .remove-image {
                position: absolute;
                top: -5px;
                right: -5px;
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 50%;
                padding: 0 6px;
                cursor: pointer;
            }
            #car-gallery-container {
                margin: 10px 0;
                min-height: 50px;
                border: 1px dashed #ccc;
                padding: 10px;
            }
            .slider-section {
                border: 1px solid #eee;
                padding: 15px;
                margin-bottom: 20px;
                background: #fff;
            }
            .slider-section h4 {
                margin-top: 0;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
            }
        </style>

        <script>
        jQuery(document).ready(function($) {
            var galleryFrame;
            function updateGalleryField() {
                var ids = [];
                $('#car-gallery-container .gallery-image').each(function() {
                    var id = $(this).data('id');
                    console.log('Found gallery-image with data-id:', id);
                    ids.push(id);
                });
                var $input = $('input[name="car_gallery"]');
                console.log('Target input:', $input.length ? $input[0] : 'NOT FOUND');
                $input.val(ids.join(','));
                console.log('Updating car_gallery field with:', ids.join(','), 'Current value:', $input.val());
            }
            // On page load, always sync hidden field with gallery container
            updateGalleryField();
            $('#add-gallery-images').on('click', function(e) {
                e.preventDefault();
                if (galleryFrame) {
                    galleryFrame.open();
                    return;
                }
                galleryFrame = wp.media({
                    title: '<?php echo esc_js(__('Select Gallery Images', 'dstweaks')); ?>',
                    button: { text: '<?php echo esc_js(__('Add to Gallery', 'dstweaks')); ?>' },
                    multiple: true
                });
                galleryFrame.on('select', function() {
                    var attachments = galleryFrame.state().get('selection').toJSON();
                    var existingIds = [];
                    $('#car-gallery-container .gallery-image').each(function() {
                        existingIds.push($(this).data('id'));
                    });
                    attachments.forEach(function(attachment) {
                        if (!existingIds.includes(attachment.id)) {
                            $('#car-gallery-container').append(
                                '<div class="gallery-image" data-id="' + parseInt(attachment.id) + '">' +
                                '<img src="' + attachment.sizes.thumbnail.url + '" />' +
                                '<button type="button" class="remove-image">×</button>' +
                                '</div>'
                            );
                        }
                    });
                    console.log('Calling updateGalleryField after select');
                    updateGalleryField();
                });
                galleryFrame.open();
            });
            // Remove Gallery Image
            $(document).on('click', '.gallery-image .remove-image', function() {
                $(this).parent().remove();
                console.log('Calling updateGalleryField after remove');
                updateGalleryField();
            });
            // On page load, ensure hidden field is always a valid string
            var initialVal = $('input[name="car_gallery"]').val();
            if (typeof initialVal !== 'string') {
                $('input[name="car_gallery"]').val('');
            }
            // Ensure hidden field is updated before form submit (robust selector)
            $('form#post').on('submit', function() {
                console.log('Calling updateGalleryField before submit');
                updateGalleryField();
            });
        });
        </script>
        <?php
    }

    // Public method to get field data by slug for a given post
    public function get_field_data_by_slug($post_id, $slug) {
        // Optionally, validate slug exists in $this->car_fields
        $valid = false;
        foreach ($this->car_fields as $field) {
            if ($field['slug'] === $slug) {
                $valid = true;
                break;
            }
        }
        if (!$valid) {
            return null; // or throw an exception if you prefer
        }
        return get_post_meta($post_id, $slug, true);
    }

    // Public method to get the car_fields array data by slug
    public function get_field_info_by_slug($slug) {
        foreach ($this->car_fields as $field) {
            if ($field['slug'] === $slug) {
                return $field;
            }
        }
        return null;
    }

    /**
     * Sanitize float field values
     *
     * @param mixed $value The value to sanitize
     * @return string Sanitized float value as string
     */
    public function sanitize_float_field($value) {
        // Remove any non-numeric characters except decimal point and minus sign
        $value = preg_replace('/[^0-9.-]/', '', $value);

        // Ensure only one decimal point
        $parts = explode('.', $value);
        if (count($parts) > 2) {
            $value = $parts[0] . '.' . implode('', array_slice($parts, 1));
        }

        // Validate as float and return as string to preserve decimals
        if (is_numeric($value)) {
            return (string) floatval($value);
        }

        return '';
    }

    /**
     * Render a select field based on field configuration
     *
     * @param array $field Field configuration from $car_fields
     * @param int $post_id Post ID
     * @return void
     */
    private function render_select_field($field, $post_id) {
        $current_value = get_post_meta($post_id, $field['slug'], true);
        $field_id = esc_attr($field['slug']);
        $field_label = esc_html(__($field['label_trans'], 'dstweaks'));

        echo '<p>';
        echo '<label for="' . $field_id . '">' . $field_label . ':</label>';
        echo '<select id="' . $field_id . '" name="' . $field_id . '">';

        // Default empty option
        $select_text = sprintf(__('Select %s', 'dstweaks'), $field['label_trans']);
        echo '<option value="">' . esc_html($select_text) . '</option>';

        // Render options from field configuration
        if (isset($field['options']) && is_array($field['options'])) {
            foreach ($field['options'] as $value => $label) {
                $selected = ($current_value === $value) ? 'selected' : '';
                $option_label = esc_html(__($label, 'dstweaks'));
                echo '<option value="' . esc_attr($value) . '" ' . $selected . '>' . $option_label . '</option>';
            }
        }

        echo '</select>';
        echo '</p>';
    }

    /**
     * Render any field type based on field configuration
     *
     * @param array $field Field configuration from $car_fields
     * @param int $post_id Post ID
     * @return void
     */
    private function render_field($field, $post_id) {
        switch ($field['type']) {
            case 'select':
                $this->render_select_field($field, $post_id);
                break;
            case 'number':
            case 'float':
            case 'text':
                $current_value = get_post_meta($post_id, $field['slug'], true);
                $field_id = esc_attr($field['slug']);
                $field_label = esc_html(__($field['label_trans'], 'dstweaks'));

                if ($field['type'] === 'number') {
                    $input_type = 'number';
                    $step_attr = '';
                } elseif ($field['type'] === 'float') {
                    $input_type = 'number';
                    $step_attr = ' step="0.01"';
                } else {
                    $input_type = 'text';
                    $step_attr = '';
                }

                echo '<p>';
                echo '<label for="' . $field_id . '">' . $field_label . ':</label>';
                echo '<input type="' . $input_type . '" id="' . $field_id . '" name="' . $field_id . '" value="' . esc_attr($current_value) . '"' . $step_attr . '>';
                echo '</p>';
                break;
            // Add more field types as needed
        }
    }

    /**
     * Save the car meta box data
     *
     * @param int $post_id Post ID
     * @return void
     */
    public function save_car_meta($post_id) {
        if (!isset($_POST['car_meta_box_nonce']) || !wp_verify_nonce($_POST['car_meta_box_nonce'], 'car_meta_box')) {
            return;
        }
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        foreach ($this->car_fields as $field) {
            $slug = $field['slug'];
            if (!isset($_POST[$slug])) continue;
            $value = $_POST[$slug];
            switch ($field['type']) {
                case 'number':
                case 'image':
                case 'file':
                    update_post_meta($post_id, $slug, absint($value));
                    break;
                case 'float':
                    // Store as string to preserve decimal precision
                    $sanitized_value = $this->sanitize_float_field($value);
                    update_post_meta($post_id, $slug, $sanitized_value);
                    break;
                case 'text':
                    update_post_meta($post_id, $slug, sanitize_text_field($value));
                    break;
                case 'textarea':
                    update_post_meta($post_id, $slug, wp_kses_post($value));
                    break;
                case 'gallery':
                    $gallery = preg_replace('/[^0-9,]/', '', trim($value));
                    update_post_meta($post_id, $slug, $gallery);
                    break;
                case 'select':
                    update_post_meta($post_id, $slug, sanitize_text_field($value));
                    break;
            }
        }
    }
}

if (class_exists('DSTweaks_Cars_Module') && !isset($GLOBALS['dstweaks_modules']['cars']['obj'])) {
    $GLOBALS['dstweaks_modules']['cars']['obj'] = new DSTweaks_Cars_Module();
} else 
    $GLOBALS['dstweaks_modules']['cars']['errors'] = "Instantiation failed. Couldn\'t find the required class.";